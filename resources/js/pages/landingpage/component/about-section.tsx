import { motion } from 'framer-motion';

export default function AboutSection() {
    return (
        <section id="tentang" className="py-20 bg-gray-50 dark:bg-gray-800">
            <div className="mx-auto max-w-7xl px-4">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                    <motion.div
                        initial={{ opacity: 0, x: -30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <h3 className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                            Tentang SMA Negeri Contoh
                        </h3>
                        <p className="text-lg text-gray-700 dark:text-gray-200 mb-6 leading-relaxed">
                            SMA Negeri Contoh adalah institusi pendidikan yang berkomitmen untuk menghasilkan
                            generasi muda yang cerdas, be<PERSON><PERSON><PERSON>, dan siap menghadapi tantangan masa depan.
                            Dengan fasilitas modern dan tenaga pengajar berpeng<PERSON>man, kami memberikan pendidikan
                            berkualitas tinggi yang mengintegrasikan akademik, karakter, dan teknologi.
                        </p>
                    </motion.div>
                    <motion.div
                        initial={{ opacity: 0, x: 30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                        className="relative"
                    >
                        <div className="absolute-bottom-6 -left-6 bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-2xl border border-gray-100 dark:border-gray-700 max-w-2xl backdrop-blur-sm">
                            {/* Vision Section */}
                            <div className="mb-8">
                                <div className="flex items-center gap-4 mb-4">
                                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full">
                                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    </div>
                                    <h4 className="text-xl font-bold text-gray-800 dark:text-white">Visi</h4>
                                </div>
                                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-5 rounded-xl border-l-4 border-green-500">
                                    <p className="text-gray-700 dark:text-gray-200 leading-relaxed font-medium text-base">
                                        Terwujudnya peserta didik yang bertakwa, cerdas, kreatif, inovatif dan berbudaya
                                    </p>
                                </div>
                            </div>
                            {/* Mission Section */}
                            <div>
                                <div className="flex items-center gap-4 mb-6">
                                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full">
                                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                        </svg>
                                    </div>
                                    <h4 className="text-xl font-bold text-gray-800 dark:text-white">Misi</h4>
                                </div>
                                <div className="space-y-5">
                                    <strong className='block text-gray-800 dark:text-white font-semibold'>Bertakwa</strong>
                                    <p className="text-gray-700 dark:text-gray-200 mt-1 leading-relaxed">
                                    Terwujudnya kesadaran siswa dalam melaksanakan ibadah (sholat wajib, sholat dhuha, puasa, tadarus al-Qur'an) dan penerapan nilai-nilai ajaran agama dengan penuh rasa tanggung jawab.
                                    </p>
                                    <strong className='block text-gray-800 dark:text-white font-semibold'>Cerdas</strong>
                                    <p className="text-gray-700 dark:text-gray-200 mt-1 leading-relaxed">
                                    Terwujudnya generasi yang memiliki perkembangan akal budi, dan pola pikir yang lebih baik.
                                    </p>
                                    <strong className='block text-gray-800 dark:text-white font-semibold'>Kreatif</strong>
                                    <p className="text-gray-700 dark:text-gray-200 mt-1 leading-relaxed">
                                    Terwujudnya generasi yang memiliki pengetahuan dan keterampilan yang mampu menciptakan hasil karya di bidang teknologi informasi, seni dan budaya.
                                    </p>
                                    <strong className='block text-gray-800 dark:text-white font-semibold'>Inovatif</strong>
                                    <p className="text-gray-700 dark:text-gray-200 mt-1 leading-relaxed">
                                    Terwujutnya generasi yang memiliki pengetahuan dan keterampilan serta mampu menemukan karya-karya baru di bidang teknologi informasi, seni dan budaya.
                                    </p>
                                    <strong className='block text-gray-800 dark:text-white font-semibold'>Berbudaya</strong>
                                    <p className="text-gray-700 dark:text-gray-200 mt-1 leading-relaxed">
                                    Terwujudnya karakter siswa yang taat tata tertib, peduli lingkungan, cinta budaya bangsa.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}